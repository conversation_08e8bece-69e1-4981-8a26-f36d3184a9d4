name: riderlink_guest
description: "Guest App for Riderlink"

publish_to: 'none' # Remove this line if you wish to publish to pub.dev


version: 0.0.1+1

environment:
  sdk: ^3.8.1


dependencies:
  flutter:
    sdk: flutter


  cupertino_icons: ^1.0.8
  intl: ^0.20.2
  provider: ^6.1.5+1
  qr_code_scanner_plus: ^2.0.10+1
  cached_network_image: ^3.4.1
  shared_preferences: ^2.5.3
  firebase_core: ^4.0.0
  firebase_messaging: ^16.0.0
  go_router: ^16.2.0
  shimmer: ^3.0.0
  http: ^1.5.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^5.0.0


flutter:

  uses-material-design: true

  assets:
   - assets/images/



  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
