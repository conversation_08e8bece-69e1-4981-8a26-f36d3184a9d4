import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:galanti_mobile/screens/guest/all_categories/all-categories.dart';
import 'package:galanti_mobile/screens/guest/search/search_results_filter.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import '../common/constants.dart';
import '../enums/categories.dart';
import '../screens/admin/agenda/agenda_screen.dart';
import '../screens/admin/app.dart';
import '../screens/admin/my_events/my_events.dart';
import '../screens/admin/service_requests/service_requests_screen.dart';
import '../screens/admin/tasks/tasks_screen.dart';
import '../screens/common/authentication/authentication.vm.dart';
import '../screens/common/authentication/login/login.dart';
import '../screens/common/authentication/registration/registration.dart';

import '../screens/common/profile/profile.dart';
import '../screens/guest/all_categories/event_categories_result.dart';
import '../screens/guest/app.dart';
import '../screens/guest/booking_history/booking_history.dart';
import '../screens/guest/emergency/emergency.dart';
import '../screens/guest/event_details/event_details.dart';
import '../screens/guest/favourite/favourite.dart';
import '../screens/guest/home/<USER>';
import '../screens/guest/my_bookings/my_booking.dart';
import '../screens/guest/service_requests/user_service_requests_history.dart';
import '../screens/guest/ticket/ticket_details/ticket_details_page.dart';
import '../screens/guest/ticket/ticket_page.dart';
import 'router_path.dart';

BuildContext? get globalNavigatorState => globalNavigatorKey.currentContext;

GoRouter getRouterConfig(String initialRoute) {
  return GoRouter(
    navigatorKey: globalNavigatorKey,
    initialLocation: initialRoute,
    routes: [

    ],
  );
}

