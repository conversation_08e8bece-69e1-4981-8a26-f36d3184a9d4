import 'package:flutter/material.dart';

/// App color constants for RiderLink Guest App
class AppColors {
  // Primary Colors
  static const Color primary = Color(0xFF4A90E2);
  static const Color primaryDark = Color(0xFF357ABD);
  static const Color primaryLight = Color(0xFF6BA3E8);
  
  // Secondary Colors
  static const Color secondary = Color(0xFF50C878);
  static const Color secondaryDark = Color(0xFF3DA55C);
  static const Color secondaryLight = Color(0xFF6DD089);
  
  // Accent Colors
  static const Color accent = Color(0xFFFF6B6B);
  static const Color accentDark = Color(0xFFE55555);
  static const Color accentLight = Color(0xFFFF8888);
  
  // Neutral Colors - Light Theme
  static const Color backgroundLight = Color(0xFFF8F9FA);
  static const Color surfaceLight = Color(0xFFFFFFFF);
  static const Color cardLight = Color(0xFFFFFFFF);
  static const Color dividerLight = Color(0xFFE9ECEF);
  
  // Neutral Colors - Dark Theme
  static const Color backgroundDark = Color(0xFF121212);
  static const Color surfaceDark = Color(0xFF1E1E1E);
  static const Color cardDark = Color(0xFF2D2D2D);
  static const Color dividerDark = Color(0xFF3A3A3A);
  
  // Text Colors - Light Theme
  static const Color textPrimaryLight = Color(0xFF212529);
  static const Color textSecondaryLight = Color(0xFF6C757D);
  static const Color textTertiaryLight = Color(0xFF9CA3AF);
  
  // Text Colors - Dark Theme
  static const Color textPrimaryDark = Color(0xFFFFFFFF);
  static const Color textSecondaryDark = Color(0xFFB3B3B3);
  static const Color textTertiaryDark = Color(0xFF8A8A8A);
  
  // Status Colors
  static const Color success = Color(0xFF28A745);
  static const Color warning = Color(0xFFFFC107);
  static const Color error = Color(0xFFDC3545);
  static const Color info = Color(0xFF17A2B8);
  
  // Progress Colors
  static const Color progressBackground = Color(0xFFE9ECEF);
  static const Color progressFill = Color(0xFF4A90E2);
  
  // Route Timeline Colors
  static const Color routeCompleted = Color(0xFF28A745);
  static const Color routeCurrent = Color(0xFF4A90E2);
  static const Color routePending = Color(0xFF9CA3AF);
  
  // Service Category Colors
  static const Color foodService = Color(0xFFFF6B6B);
  static const Color medicalService = Color(0xFF4ECDC4);
  static const Color restroomService = Color(0xFF45B7D1);
  static const Color giftShopService = Color(0xFFFFD93D);
  static const Color pickupService = Color(0xFF6C5CE7);
  
  // Transparent Colors
  static const Color transparent = Colors.transparent;
  static const Color overlay = Color(0x80000000);
  static const Color shimmer = Color(0xFFE0E0E0);
}
