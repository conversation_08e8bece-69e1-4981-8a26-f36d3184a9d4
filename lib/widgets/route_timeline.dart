import 'package:flutter/material.dart';
import '../common/constants/app_colors.dart';
import '../common/constants/app_text_styles.dart';

/// Route timeline widget showing journey progress
class RouteTimeline extends StatelessWidget {
  final List<RouteStop> stops;
  final EdgeInsetsGeometry? padding;

  const RouteTimeline({
    super.key,
    required this.stops,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return Container(
      padding: padding ?? const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Real-Time Route',
            style: AppTextStyles.heading5.copyWith(
              color: isDarkMode 
                  ? AppColors.textPrimaryDark 
                  : AppColors.textPrimaryLight,
            ),
          ),
          const SizedBox(height: 16),
          ...stops.asMap().entries.map((entry) {
            final index = entry.key;
            final stop = entry.value;
            final isLast = index == stops.length - 1;
            
            return RouteStopItem(
              stop: stop,
              isLast: isLast,
            );
          }).toList(),
        ],
      ),
    );
  }
}

/// Individual route stop item
class RouteStopItem extends StatelessWidget {
  final RouteStop stop;
  final bool isLast;

  const RouteStopItem({
    super.key,
    required this.stop,
    this.isLast = false,
  });

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    Color getStatusColor() {
      switch (stop.status) {
        case RouteStopStatus.completed:
          return AppColors.routeCompleted;
        case RouteStopStatus.current:
          return AppColors.routeCurrent;
        case RouteStopStatus.pending:
          return AppColors.routePending;
      }
    }

    IconData getStatusIcon() {
      switch (stop.status) {
        case RouteStopStatus.completed:
          return Icons.check_circle;
        case RouteStopStatus.current:
          return Icons.location_on;
        case RouteStopStatus.pending:
          return Icons.radio_button_unchecked;
      }
    }

    return IntrinsicHeight(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Timeline indicator
          Column(
            children: [
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: getStatusColor(),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  getStatusIcon(),
                  color: Colors.white,
                  size: 18,
                ),
              ),
              if (!isLast)
                Container(
                  width: 2,
                  height: 40,
                  color: isDarkMode 
                      ? AppColors.dividerDark 
                      : AppColors.dividerLight,
                  margin: const EdgeInsets.symmetric(vertical: 4),
                ),
            ],
          ),
          
          const SizedBox(width: 16),
          
          // Stop information
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Text(
                        stop.locationName,
                        style: AppTextStyles.locationName.copyWith(
                          color: isDarkMode 
                              ? AppColors.textPrimaryDark 
                              : AppColors.textPrimaryLight,
                        ),
                      ),
                    ),
                    if (stop.estimatedTime != null)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: getStatusColor(),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          stop.estimatedTime!,
                          style: AppTextStyles.bodySmall.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                  ],
                ),
                
                if (stop.subtitle != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    stop.subtitle!,
                    style: AppTextStyles.locationSubtitle.copyWith(
                      color: isDarkMode 
                          ? AppColors.textSecondaryDark 
                          : AppColors.textSecondaryLight,
                    ),
                  ),
                ],
                
                if (stop.distanceToGo != null) ...[
                  const SizedBox(height: 8),
                  Text(
                    stop.distanceToGo!,
                    style: AppTextStyles.distanceText.copyWith(
                      color: isDarkMode 
                          ? AppColors.textTertiaryDark 
                          : AppColors.textTertiaryLight,
                    ),
                  ),
                ],
                
                if (!isLast) const SizedBox(height: 16),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// Route stop data model
class RouteStop {
  final String locationName;
  final String? subtitle;
  final String? distanceToGo;
  final String? estimatedTime;
  final RouteStopStatus status;

  const RouteStop({
    required this.locationName,
    this.subtitle,
    this.distanceToGo,
    this.estimatedTime,
    required this.status,
  });
}

/// Route stop status enum
enum RouteStopStatus {
  completed,
  current,
  pending,
}

/// Sample route stops for demo
class SampleRouteStops {
  static List<RouteStop> get demoStops => [
    const RouteStop(
      locationName: 'Chennai, TN',
      status: RouteStopStatus.completed,
    ),
    const RouteStop(
      locationName: 'SARAVANA BHAVAN',
      subtitle: 'Coimbatore, TN',
      distanceToGo: '37 Km To Go',
      estimatedTime: '8:00 PM',
      status: RouteStopStatus.completed,
    ),
    const RouteStop(
      locationName: 'PUBLIC RESTROOM',
      subtitle: 'Madurai, TN',
      distanceToGo: '94 Km To Go',
      estimatedTime: '9:30 PM',
      status: RouteStopStatus.current,
    ),
    const RouteStop(
      locationName: 'MTR RESTAURANT',
      subtitle: 'Bengaluru, KA',
      estimatedTime: '23:00 PM',
      status: RouteStopStatus.pending,
    ),
    const RouteStop(
      locationName: 'Bengaluru, KA',
      status: RouteStopStatus.pending,
    ),
  ];
}
