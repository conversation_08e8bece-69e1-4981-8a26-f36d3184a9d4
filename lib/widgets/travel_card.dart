import 'package:flutter/material.dart';
import '../common/constants/app_colors.dart';
import '../common/constants/app_text_styles.dart';

/// Travel card widget displaying bus journey information
class TravelCard extends StatelessWidget {
  final String busOperator;
  final String busType;
  final String busNumber;
  final String fromLocation;
  final String toLocation;
  final String routeNumber;
  final String departureTime;
  final String arrivalTime;
  final double progressPercentage;
  final VoidCallback? onTap;

  const TravelCard({
    super.key,
    required this.busOperator,
    required this.busType,
    required this.busNumber,
    required this.fromLocation,
    required this.toLocation,
    required this.routeNumber,
    required this.departureTime,
    required this.arrivalTime,
    required this.progressPercentage,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with operator and bus number
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        busOperator,
                        style: AppTextStyles.labelLarge.copyWith(
                          color: isDarkMode 
                              ? AppColors.textPrimaryDark 
                              : AppColors.textPrimaryLight,
                        ),
                      ),
                      Text(
                        busType,
                        style: AppTextStyles.bodySmall.copyWith(
                          color: isDarkMode 
                              ? AppColors.textSecondaryDark 
                              : AppColors.textSecondaryLight,
                        ),
                      ),
                    ],
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        'Bus No.',
                        style: AppTextStyles.bodySmall.copyWith(
                          color: isDarkMode 
                              ? AppColors.textSecondaryDark 
                              : AppColors.textSecondaryLight,
                        ),
                      ),
                      Text(
                        busNumber,
                        style: AppTextStyles.busNumber.copyWith(
                          color: isDarkMode 
                              ? AppColors.textPrimaryDark 
                              : AppColors.textPrimaryLight,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              
              const SizedBox(height: 16),
              
              // Route information
              Text(
                '$fromLocation → $toLocation',
                style: AppTextStyles.routeName.copyWith(
                  color: AppColors.primary,
                ),
              ),
              
              const SizedBox(height: 4),
              
              Text(
                'Route No : $routeNumber',
                style: AppTextStyles.routeNumber.copyWith(
                  color: isDarkMode 
                      ? AppColors.textSecondaryDark 
                      : AppColors.textSecondaryLight,
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Time and progress section
              Row(
                children: [
                  // Departure info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Departed',
                          style: AppTextStyles.bodySmall.copyWith(
                            color: isDarkMode 
                                ? AppColors.textSecondaryDark 
                                : AppColors.textSecondaryLight,
                          ),
                        ),
                        Text(
                          departureTime,
                          style: AppTextStyles.timeText.copyWith(
                            color: isDarkMode 
                                ? AppColors.textPrimaryDark 
                                : AppColors.textPrimaryLight,
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  // Progress section
                  Expanded(
                    flex: 2,
                    child: Column(
                      children: [
                        Text(
                          '${progressPercentage.toInt()}% Completed',
                          style: AppTextStyles.progressText.copyWith(
                            color: AppColors.primary,
                          ),
                        ),
                        const SizedBox(height: 8),
                        LinearProgressIndicator(
                          value: progressPercentage / 100,
                          backgroundColor: isDarkMode 
                              ? AppColors.dividerDark 
                              : AppColors.progressBackground,
                          valueColor: const AlwaysStoppedAnimation<Color>(
                            AppColors.progressFill,
                          ),
                          minHeight: 6,
                          borderRadius: BorderRadius.circular(3),
                        ),
                      ],
                    ),
                  ),
                  
                  // Arrival info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          'ETA',
                          style: AppTextStyles.bodySmall.copyWith(
                            color: isDarkMode 
                                ? AppColors.textSecondaryDark 
                                : AppColors.textSecondaryLight,
                          ),
                        ),
                        Text(
                          arrivalTime,
                          style: AppTextStyles.timeText.copyWith(
                            color: isDarkMode 
                                ? AppColors.textPrimaryDark 
                                : AppColors.textPrimaryLight,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
