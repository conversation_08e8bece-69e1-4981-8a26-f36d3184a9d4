import 'package:flutter/material.dart';
import '../common/constants/app_colors.dart';
import '../common/constants/app_text_styles.dart';

/// Service icon button widget for quick access to services
class ServiceIconButton extends StatelessWidget {
  final IconData icon;
  final String label;
  final VoidCallback? onTap;
  final Color? iconColor;
  final Color? backgroundColor;
  final bool isSelected;

  const ServiceIconButton({
    super.key,
    required this.icon,
    required this.label,
    this.onTap,
    this.iconColor,
    this.backgroundColor,
    this.isSelected = false,
  });

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    Color getServiceColor() {
      switch (label.toLowerCase()) {
        case 'food':
          return AppColors.foodService;
        case 'medical shops':
          return AppColors.medicalService;
        case 'restroom':
          return AppColors.restroomService;
        case 'gift shops':
          return AppColors.giftShopService;
        case 'pickups':
          return AppColors.pickupService;
        default:
          return AppColors.primary;
      }
    }

    final Color effectiveIconColor = iconColor ?? getServiceColor();
    final Color effectiveBackgroundColor = backgroundColor ?? 
        (isDarkMode ? AppColors.cardDark : AppColors.cardLight);

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 12),
        decoration: BoxDecoration(
          color: isSelected 
              ? effectiveIconColor.withOpacity(0.1)
              : effectiveBackgroundColor,
          borderRadius: BorderRadius.circular(12),
          border: isSelected 
              ? Border.all(color: effectiveIconColor, width: 1.5)
              : Border.all(
                  color: isDarkMode 
                      ? AppColors.dividerDark 
                      : AppColors.dividerLight,
                  width: 1,
                ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: effectiveIconColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                size: 24,
                color: effectiveIconColor,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: AppTextStyles.serviceLabel.copyWith(
                color: isSelected 
                    ? effectiveIconColor
                    : (isDarkMode 
                        ? AppColors.textPrimaryDark 
                        : AppColors.textPrimaryLight),
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
}

/// Grid of service icon buttons
class ServiceIconGrid extends StatelessWidget {
  final List<ServiceIconData> services;
  final int crossAxisCount;
  final double childAspectRatio;
  final EdgeInsetsGeometry? padding;

  const ServiceIconGrid({
    super.key,
    required this.services,
    this.crossAxisCount = 3,
    this.childAspectRatio = 1.0,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding ?? const EdgeInsets.all(16),
      child: GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: crossAxisCount,
          childAspectRatio: childAspectRatio,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
        ),
        itemCount: services.length,
        itemBuilder: (context, index) {
          final service = services[index];
          return ServiceIconButton(
            icon: service.icon,
            label: service.label,
            onTap: service.onTap,
            iconColor: service.iconColor,
            backgroundColor: service.backgroundColor,
            isSelected: service.isSelected,
          );
        },
      ),
    );
  }
}

/// Data class for service icon information
class ServiceIconData {
  final IconData icon;
  final String label;
  final VoidCallback? onTap;
  final Color? iconColor;
  final Color? backgroundColor;
  final bool isSelected;

  const ServiceIconData({
    required this.icon,
    required this.label,
    this.onTap,
    this.iconColor,
    this.backgroundColor,
    this.isSelected = false,
  });
}

/// Predefined service icons
class ServiceIcons {
  static const ServiceIconData food = ServiceIconData(
    icon: Icons.restaurant,
    label: 'Food',
  );

  static const ServiceIconData medicalShops = ServiceIconData(
    icon: Icons.local_pharmacy,
    label: 'Medical Shops',
  );

  static const ServiceIconData restroom = ServiceIconData(
    icon: Icons.wc,
    label: 'Restroom',
  );

  static const ServiceIconData giftShops = ServiceIconData(
    icon: Icons.card_giftcard,
    label: 'Gift Shops',
  );

  static const ServiceIconData pickups = ServiceIconData(
    icon: Icons.local_shipping,
    label: 'Pickups',
  );

  static List<ServiceIconData> get defaultServices => [
    food,
    medicalShops,
    restroom,
    giftShops,
    pickups,
  ];
}
