import 'package:flutter/material.dart';
import '../common/constants/app_colors.dart';
import '../common/constants/app_text_styles.dart';

/// Custom app header widget
class AppHeader extends StatelessWidget implements PreferredSizeWidget {
  final String? title;
  final Widget? leading;
  final List<Widget>? actions;
  final bool showBackButton;
  final VoidCallback? onBackPressed;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final double elevation;
  final Widget? flexibleSpace;
  final PreferredSizeWidget? bottom;

  const AppHeader({
    super.key,
    this.title,
    this.leading,
    this.actions,
    this.showBackButton = false,
    this.onBackPressed,
    this.backgroundColor,
    this.foregroundColor,
    this.elevation = 0,
    this.flexibleSpace,
    this.bottom,
  });

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return AppBar(
      title: title != null 
          ? Text(
              title!,
              style: AppTextStyles.heading5.copyWith(
                color: foregroundColor ?? 
                    (isDarkMode 
                        ? AppColors.textPrimaryDark 
                        : AppColors.textPrimaryLight),
              ),
            )
          : null,
      leading: leading ?? 
          (showBackButton 
              ? IconButton(
                  icon: const Icon(Icons.arrow_back_ios),
                  onPressed: onBackPressed ?? () => Navigator.of(context).pop(),
                )
              : null),
      actions: actions,
      backgroundColor: backgroundColor ?? 
          (isDarkMode ? AppColors.surfaceDark : AppColors.surfaceLight),
      foregroundColor: foregroundColor ?? 
          (isDarkMode 
              ? AppColors.textPrimaryDark 
              : AppColors.textPrimaryLight),
      elevation: elevation,
      scrolledUnderElevation: elevation > 0 ? elevation + 1 : 1,
      flexibleSpace: flexibleSpace,
      bottom: bottom,
      centerTitle: false,
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(
    kToolbarHeight + (bottom?.preferredSize.height ?? 0),
  );
}

/// Greeting header widget
class GreetingHeader extends StatelessWidget {
  final String userName;
  final String greeting;
  final String subtitle;
  final Widget? avatar;
  final List<Widget>? actions;
  final EdgeInsetsGeometry? padding;

  const GreetingHeader({
    super.key,
    required this.userName,
    this.greeting = 'Hello',
    this.subtitle = 'Stay fed. Stay on track!',
    this.avatar,
    this.actions,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return Container(
      padding: padding ?? const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      '$greeting ',
                      style: AppTextStyles.heading4.copyWith(
                        color: isDarkMode 
                            ? AppColors.textPrimaryDark 
                            : AppColors.textPrimaryLight,
                      ),
                    ),
                    Text(
                      userName,
                      style: AppTextStyles.heading4.copyWith(
                        color: isDarkMode 
                            ? AppColors.textPrimaryDark 
                            : AppColors.textPrimaryLight,
                      ),
                    ),
                    const SizedBox(width: 4),
                    const Text('👋', style: TextStyle(fontSize: 20)),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: isDarkMode 
                        ? AppColors.textSecondaryDark 
                        : AppColors.textSecondaryLight,
                  ),
                ),
              ],
            ),
          ),
          if (actions != null) ...actions!,
          if (avatar != null) ...[
            const SizedBox(width: 16),
            avatar!,
          ],
        ],
      ),
    );
  }
}

/// Profile avatar widget
class ProfileAvatar extends StatelessWidget {
  final String? imageUrl;
  final String? initials;
  final double size;
  final VoidCallback? onTap;
  final Color? backgroundColor;
  final Color? textColor;

  const ProfileAvatar({
    super.key,
    this.imageUrl,
    this.initials,
    this.size = 40,
    this.onTap,
    this.backgroundColor,
    this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          color: backgroundColor ?? AppColors.primary,
          shape: BoxShape.circle,
          border: Border.all(
            color: isDarkMode 
                ? AppColors.dividerDark 
                : AppColors.dividerLight,
            width: 2,
          ),
        ),
        child: imageUrl != null
            ? ClipOval(
                child: Image.network(
                  imageUrl!,
                  width: size,
                  height: size,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return _buildInitialsAvatar();
                  },
                ),
              )
            : _buildInitialsAvatar(),
      ),
    );
  }

  Widget _buildInitialsAvatar() {
    return Center(
      child: Text(
        initials ?? '?',
        style: TextStyle(
          color: textColor ?? Colors.white,
          fontSize: size * 0.4,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }
}

/// Search header widget
class SearchHeader extends StatelessWidget {
  final String? hintText;
  final TextEditingController? controller;
  final Function(String)? onChanged;
  final Function(String)? onSubmitted;
  final VoidCallback? onFilterTap;
  final bool showFilter;
  final EdgeInsetsGeometry? padding;

  const SearchHeader({
    super.key,
    this.hintText,
    this.controller,
    this.onChanged,
    this.onSubmitted,
    this.onFilterTap,
    this.showFilter = false,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return Container(
      padding: padding ?? const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: controller,
              onChanged: onChanged,
              onSubmitted: onSubmitted,
              decoration: InputDecoration(
                hintText: hintText ?? 'Search...',
                prefixIcon: const Icon(Icons.search),
                filled: true,
                fillColor: isDarkMode 
                    ? AppColors.cardDark 
                    : AppColors.cardLight,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide.none,
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
            ),
          ),
          if (showFilter) ...[
            const SizedBox(width: 12),
            IconButton(
              onPressed: onFilterTap,
              icon: const Icon(Icons.tune),
              style: IconButton.styleFrom(
                backgroundColor: isDarkMode 
                    ? AppColors.cardDark 
                    : AppColors.cardLight,
                foregroundColor: isDarkMode 
                    ? AppColors.textPrimaryDark 
                    : AppColors.textPrimaryLight,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                padding: const EdgeInsets.all(12),
              ),
            ),
          ],
        ],
      ),
    );
  }
}
