import 'package:flutter/material.dart';
import '../widgets/widgets.dart';
import '../common/constants/constants.dart';

/// Demo screen showcasing all theme components
class DemoScreen extends StatefulWidget {
  const DemoScreen({super.key});

  @override
  State<DemoScreen> createState() => _DemoScreenState();
}

class _DemoScreenState extends State<DemoScreen> {
  int _currentNavIndex = 0;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CustomScrollView(
        slivers: [
          // App Header with greeting
          SliverToBoxAdapter(
            child: GreetingHeader(
              userName: 'Alwin',
              avatar: ProfileAvatar(
                initials: 'A',
                onTap: () {
                  // Handle profile tap
                },
              ),
            ),
          ),
          
          // Travel Card
          SliverToBoxAdapter(
            child: TravelCard(
              busOperator: 'KPM TRAVELS',
              busType: 'AC Deluxe',
              busNumber: 'KA09A1968',
              fromLocation: 'Chennai',
              toLocation: 'Bengaluru',
              routeNumber: 'CHN007',
              departureTime: '09:00 AM',
              arrivalTime: '21:00 PM',
              progressPercentage: 46,
              onTap: () {
                // Handle card tap
              },
            ),
          ),
          
          // Service Icons
          SliverToBoxAdapter(
            child: ServiceIconGrid(
              services: ServiceIcons.defaultServices,
              crossAxisCount: 3,
              childAspectRatio: 1.0,
            ),
          ),
          
          // Route Timeline
          SliverToBoxAdapter(
            child: RouteTimeline(
              stops: SampleRouteStops.demoStops,
            ),
          ),
          
          // Buttons Demo
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Text(
                    'Button Examples',
                    style: AppTextStyles.heading5,
                  ),
                  const SizedBox(height: 16),
                  
                  // Primary Button
                  PrimaryButton(
                    text: 'Primary Button',
                    onPressed: () {},
                    icon: Icons.check,
                  ),
                  
                  const SizedBox(height: 12),
                  
                  // Secondary Button
                  SecondaryButton(
                    text: 'Secondary Button',
                    onPressed: () {},
                    icon: Icons.favorite_outline,
                  ),
                  
                  const SizedBox(height: 12),
                  
                  // Loading Button
                  PrimaryButton(
                    text: 'Loading...',
                    isLoading: true,
                    onPressed: () {},
                  ),
                  
                  const SizedBox(height: 12),
                  
                  // Disabled Button
                  PrimaryButton(
                    text: 'Disabled Button',
                    isDisabled: true,
                    onPressed: () {},
                  ),
                ],
              ),
            ),
          ),
          
          // Text Styles Demo
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('Heading 1', style: AppTextStyles.heading1),
                  Text('Heading 2', style: AppTextStyles.heading2),
                  Text('Heading 3', style: AppTextStyles.heading3),
                  Text('Body Large', style: AppTextStyles.bodyLarge),
                  Text('Body Medium', style: AppTextStyles.bodyMedium),
                  Text('Body Small', style: AppTextStyles.bodySmall),
                  Text('Label Large', style: AppTextStyles.labelLarge),
                  Text('Caption', style: AppTextStyles.caption),
                  const SizedBox(height: 16),
                  Text('Primary Text', style: AppTextStyles.primaryText),
                  Text('Secondary Text', style: AppTextStyles.secondaryText),
                  Text('Success Text', style: AppTextStyles.successText),
                  Text('Error Text', style: AppTextStyles.errorText),
                  Text('Warning Text', style: AppTextStyles.warningText),
                ],
              ),
            ),
          ),
          
          // Add some bottom padding
          const SliverToBoxAdapter(
            child: SizedBox(height: 100),
          ),
        ],
      ),
      
      // Bottom Navigation
      bottomNavigationBar: CustomBottomNavigation(
        currentIndex: _currentNavIndex,
        onTap: (index) {
          setState(() {
            _currentNavIndex = index;
          });
        },
        items: BottomNavItems.defaultItems,
      ),
    );
  }
}

/// Theme toggle button for testing
class ThemeToggleButton extends StatelessWidget {
  const ThemeToggleButton({super.key});

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return IconButton(
      onPressed: () {
        // This would typically be handled by a theme provider
        // For demo purposes, we'll just show a snackbar
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Theme toggle would switch to ${isDarkMode ? 'Light' : 'Dark'} mode',
            ),
            duration: const Duration(seconds: 2),
          ),
        );
      },
      icon: Icon(
        isDarkMode ? Icons.light_mode : Icons.dark_mode,
      ),
      tooltip: 'Toggle Theme',
    );
  }
}
